# Contexto para o Agente - Projeto Code2Post

**Data:** 30 de Julho de 2025

## 1. Introdução ao Projeto Code2Post
O **Code2Post** é uma plataforma SaaS que conecta o GitHub ao LinkedIn, automatizando a geração de posts sobre o progresso de desenvolvimento usando inteligência artificial. Seu objetivo é ajudar desenvolvedores a transformar suas atividades de código em conteúdo envolvente para redes sociais profissionais, resolvendo a dificuldade de criar conteúdo consistente no LinkedIn.

Este projeto tem o potencial de se tornar um SaaS de grande sucesso global, e para isso, estamos implementando uma série de melhorias técnicas para garantir escalabilidade, segurança e qualidade de código.

---

## 2. Missão do Agente
Você foi designado como o agente responsável por implementar as melhorias técnicas no projeto Code2Post. Sua missão é garantir que o projeto esteja 100% em conformidade com as diretrizes técnicas estabelecidas, preparando-o para se tornar um SaaS escalável e de alto nível.

Para isso, você deve:
- Analisar e compreender os documentos de referência fornecidos.
- Revisar e corrigir o código-fonte atual para eliminar más práticas e garantir conformidade.
- Gerar um relatório técnico detalhando suas ações e a prontidão do projeto para as próximas etapas.

---

## 3. Documentos de Referência
Os seguintes documentos são fundamentais para sua tarefa e devem ser lidos e compreendidos cuidadosamente:

- **`doc-mudancas-tecnicas.md`**: Contém todas as melhorias técnicas sugeridas para transformar o projeto em um SaaS de alto nível.
- **`checklist-melhorias.md`**: Checklist técnico completo com todas as tarefas organizadas por prioridade e fase.
- **`rules-code2post.md`**: Regras técnicas obrigatórias que guiam o desenvolvimento do projeto.

**Instrução:** Antes de prosseguir, leia e compreenda esses documentos. Eles são a base para todas as ações que você realizará.

---

## 4. Primeira Tarefa do Agente
Sua primeira tarefa consiste em:

### 4.1 Analisar os Documentos de Referência
- Leia cuidadosamente os documentos `doc-mudancas-tecnicas.md`, `checklist-melhorias.md` e `rules-code2post.md`.
- Certifique-se de entender todas as diretrizes, melhorias sugeridas e regras técnicas.

### 4.2 Ler Todo o Código-Fonte Atual
- Examine o código-fonte do projeto para identificar áreas que não estão em conformidade com os documentos de referência.

### 4.3 Corrigir ou Remover Não Conformidades
- Identifique e corrija:
  - Uso indevido de bibliotecas (ex.: Octokit REST).
  - Más práticas de codificação (ex.: funções longas, código duplicado).
  - Estrutura de arquivos inadequada.
  - Nomenclatura de código incorreta (ex.: nomes genéricos como `data`).
- **Observação:** Qualquer código ou prática que viole as regras técnicas deve ser ajustado ou removido.

### 4.4 Gerar um Relatório Técnico
Ao final da análise e limpeza, gere um relatório técnico profissional contendo:
- **Correções e Remoções:** Detalhe tudo que foi corrigido ou removido, explicando o motivo.
- **Dúvidas Pendentes:** Liste quaisquer dúvidas ou áreas que necessitem de esclarecimento adicional.
- **Prontidão do Projeto:** Avalie se o projeto está pronto para prosseguir com a execução do checklist técnico (`checklist-melhorias.md`).

---

## 5. Instruções Adicionais
- Após concluir a primeira tarefa, prossiga para a implementação das melhorias conforme o checklist técnico.
- Siga rigorosamente as regras técnicas (`rules-code2post.md`) em todas as etapas futuras.
- Mantenha a documentação atualizada e garanta que o código esteja sempre em conformidade com as diretrizes estabelecidas.

---

## 6. Formato e Localização do Documento
- Este documento deve ser salvo como `contexto-agente.md` na raiz do projeto.
- Ele foi escrito em português brasileiro para garantir clareza e precisão nas instruções.

---
