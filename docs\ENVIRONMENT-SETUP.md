# 🌍 CONFIGURAÇÃO DE AMBIENTES - CODE2POST

## 📋 **VISÃO GERAL**

O projeto agora está configurado com ambientes separados para **desenvolvimento** e **produção**, evitando conflitos e garantindo segurança.

## 🗂️ **ESTRUTURA DE ARQUIVOS**

```
Code2Post/
├── frontend/
│   ├── .env                    # ← Arquivo ativo (não commitar)
│   ├── .env.development        # ← Configurações de desenvolvimento
│   └── .env.production         # ← Configurações de produção
├── backend/
│   ├── .env                    # ← Arquivo ativo (não commitar)
│   ├── .env.development        # ← Configurações de desenvolvimento
│   └── .env.production         # ← Configurações de produção
└── scripts/
    └── switch-env.js           # ← Script para trocar ambientes
```

## 🚀 **COMANDOS PRINCIPAIS**

### **Desenvolvimento (Localhost):**
```bash
# Trocar para ambiente de desenvolvimento
npm run env:dev

# Iniciar desenvolvimento (frontend + backend)
npm run dev
```

### **Produção (Deploy):**
```bash
# Trocar para ambiente de produção
npm run env:prod

# Build para produção
npm run build

# Iniciar produção
npm start
```

## ⚙️ **CONFIGURAÇÕES POR AMBIENTE**

### **🔧 DESENVOLVIMENTO:**
- **Frontend:** `http://localhost:5173`
- **Backend:** `http://localhost:3001`
- **GitHub OAuth:** App de desenvolvimento
- **Debug:** Habilitado
- **Rate Limit:** Mais permissivo

### **🚀 PRODUÇÃO:**
- **Frontend:** `https://www.code2post.com`
- **Backend:** `https://api.code2post.com`
- **GitHub OAuth:** App de produção
- **Debug:** Desabilitado
- **Rate Limit:** Mais restritivo

## 🔐 **SEGURANÇA**

### **Arquivos .env principais NÃO são commitados:**
```gitignore
# Já está no .gitignore
frontend/.env
backend/.env
```

### **Arquivos .env.development e .env.production SÃO commitados:**
- Contêm configurações específicas de cada ambiente
- Facilitam setup para novos desenvolvedores
- Mantêm consistência entre ambientes

## 🎯 **WORKFLOW RECOMENDADO**

### **Para Desenvolvimento:**
1. `git checkout feature/sua-branch`
2. `npm run env:dev`
3. `npm run dev`
4. Desenvolver e testar localmente
5. Commit das mudanças

### **Para Deploy:**
1. `git checkout main`
2. `git merge feature/sua-branch`
3. `npm run env:prod`
4. `npm run build`
5. Push para main (deploy automático)

## 🛠️ **INSTALAÇÃO INICIAL**

```bash
# Instalar dependências em todos os projetos
npm run install:all

# Instalar concurrently para rodar frontend + backend juntos
npm install
```

## 🔄 **TROCA MANUAL DE AMBIENTE**

Se precisar trocar manualmente:

```bash
# Para desenvolvimento
node scripts/switch-env.js development

# Para produção  
node scripts/switch-env.js production
```

## ⚠️ **IMPORTANTE**

- **NUNCA** commite os arquivos `.env` principais
- **SEMPRE** use `npm run env:dev` antes de desenvolver
- **SEMPRE** use `npm run env:prod` antes de fazer deploy
- **TESTE** localmente antes de fazer merge para main

## 🆘 **TROUBLESHOOTING**

### **Erro de CORS:**
- Verifique se está no ambiente correto
- Development: `http://localhost:5173`
- Production: `https://www.code2post.com`

### **Erro de GitHub OAuth:**
- Verifique se o Client ID está correto para o ambiente
- Development: `Ov23lijlSapiYARZOn5i`
- Production: `Ov23liBOFXBtP2DyCbNL`

### **Porta ocupada:**
- Backend development roda na porta `3001`
- Frontend development roda na porta `5173`
